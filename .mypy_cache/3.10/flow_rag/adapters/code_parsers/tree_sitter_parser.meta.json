{"data_mtime": 1750705745, "dep_lines": [11, 12, 9, 10, 1, 2, 3, 4, 5, 6, 7, 14, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 15], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["flow_rag.domain.ports.file_loader", "flow_rag.adapters.code_parsers.base_parser", "flow_rag.infrastructure.log_utils", "flow_rag.domain.models", "sys", "traceback", "typing", "logging", "os", "fnmatch", "collections", "tree_sitter", "warnings", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "flow_rag.domain", "flow_rag.domain.ports", "flow_rag.domain.ports.code_parser", "io", "typing_extensions"], "hash": "d9cb7756866638792e461f07428bb2efa54c011d", "id": "flow_rag.adapters.code_parsers.tree_sitter_parser", "ignore_all": false, "interface_hash": "83df119eb34e70f0625b9d065938a89bb3d77833", "mtime": 1750704098, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/adapters/code_parsers/tree_sitter_parser.py", "plugin_data": null, "size": 20202, "suppressed": ["tree_sitter_languages"], "version_id": "1.16.1"}