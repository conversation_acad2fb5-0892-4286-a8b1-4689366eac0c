"""
Vector store adapters for the FlowRAG library.

This package contains implementations of the VectorStorePort interface,
providing different storage backends for vector embeddings.
"""
from ...infrastructure.config import get_config
from ...infrastructure.log_utils import log_message
from .hybrid_store import HybridStore
import logging

class VectorStoreFactory:
    """
    Factory for creating vector store adapters.
    """

    _config_manager = get_config()

    _stores = {
        "hybrid": HybridStore,
        # "faiss": FaissStore,  # Example for future expansion
    }

    @classmethod
    def _get_vector_store_config(cls):
        return cls._config_manager.get_vector_store_config() or {}

    @classmethod
    def create_store(cls, logger: logging.Logger, **kwargs) -> 'HybridStore':
        store_type = cls._get_vector_store_config().get('provider')
        if store_type not in cls._stores:
            raise ValueError(f"Unknown vector store type: {store_type}. Available: {list(cls._stores.keys())}")
        log_message(f"# Vector store selected: {store_type}", logger=logger)
        return cls._stores[store_type](logger=logger, **kwargs)

__all__ = ['HybridStore', 'VectorStoreFactory']